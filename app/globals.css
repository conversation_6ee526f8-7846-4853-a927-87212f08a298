@import "tailwindcss";

:root {
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  margin: auto;
}

/* Light mode (default) - FORCE PRIORITY */
:root,
html,
html.light,
html.light-mode,
html:not(.dark):not(.dark-mode) {
  --background: #ffffff !important;
  --foreground: #171717 !important;
  --card: #ffffff !important;
  --card-foreground: #171717 !important;
  --border: #e5e7eb !important;
  --input: #e5e7eb !important;
  --primary: #059669 !important;
  --primary-foreground: #ffffff !important;
  --secondary: #f3f4f6 !important;
  --secondary-foreground: #374151 !important;
  --muted: #f9fafb !important;
  --muted-foreground: #6b7280 !important;
  color-scheme: light !important;
}

/* Dark mode */
html.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
  --card: #1f2937;
  --card-foreground: #f9fafb;
  --border: #374151;
  --input: #374151;
  --primary: #10b981;
  --primary-foreground: #ffffff;
  --secondary: #374151;
  --secondary-foreground: #f9fafb;
  --muted: #111827;
  --muted-foreground: #9ca3af;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Force light mode body styles */
body,
html:not(.dark) body,
html.light body,
html.light-mode body {
  background: var(--background) !important;
  color: var(--foreground) !important;
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Explicit light mode body override */
html:not(.dark):not(.dark-mode) body {
  background: #ffffff !important;
  color: #171717 !important;
}

/* Smooth transitions for all elements */
* {
  transition-property: background-color, border-color, color, box-shadow;
  transition-duration: 0.3s;
  transition-timing-function: ease;
}

/* Custom utilities for modern effects */
@utility glassmorphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@utility glassmorphism-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-muted);
}

::-webkit-scrollbar-thumb {
  background: var(--color-primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary);
  opacity: 0.8;
}