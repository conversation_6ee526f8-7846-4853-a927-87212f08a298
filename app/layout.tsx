import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: {
    default: "Ourika Travels - Discover Morocco's Hidden Valley",
    template: "%s | Ourika Travels"
  },
  description: "Explore the breathtaking Ourika Valley with expert local guides. Authentic Moroccan mountain adventures, Berber village tours, Atlas Mountains hiking, and cultural experiences near Marrakech.",
  keywords: [
    "Ourika Valley tours",
    "Morocco travel",
    "Atlas Mountains",
    "Berber villages",
    "Marrakech day trips",
    "Morocco hiking",
    "Ourika waterfalls",
    "Moroccan culture",
    "Mountain adventures",
    "Local guides Morocco"
  ],
  authors: [{ name: "Ourika Travels" }],
  creator: "Ourika Travels",
  publisher: "Ourika Travels",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://ourikatravels.com"), // Replace with your actual domain
  alternates: {
    canonical: "/",
    languages: {
      "en-US": "/en-US",
      "fr-FR": "/fr-FR",
      "ar-MA": "/ar-MA"
    }
  },
  openGraph: {
    title: "Ourika Travels - Discover Morocco's Hidden Valley",
    description: "Explore the breathtaking Ourika Valley with expert local guides. Authentic Moroccan mountain adventures, Berber village tours, and Atlas Mountains hiking.",
    url: "https://ourikatravels.com", // Replace with your actual domain
    siteName: "Ourika Travels",
    images: [
      {
        url: "/og-image.jpg", // Add your Open Graph image
        width: 1200,
        height: 630,
        alt: "Ourika Valley landscape with Atlas Mountains"
      }
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Ourika Travels - Discover Morocco's Hidden Valley",
    description: "Explore the breathtaking Ourika Valley with expert local guides. Authentic Moroccan mountain adventures and cultural experiences.",
    images: ["/twitter-image.jpg"], // Add your Twitter card image
    creator: "@ourikatravels", // Replace with your actual Twitter handle
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code", // Replace with your Google Search Console verification
    yandex: "your-yandex-verification-code", // Replace with your Yandex verification if needed
    yahoo: "your-yahoo-verification-code", // Replace with your Yahoo verification if needed
  },
  category: "Travel & Tourism",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="light">
      <head>
        {/* Force light mode as default - prevent any dark mode flash */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                const htmlElement = document.documentElement;
                const bodyElement = document.body;

                // AGGRESSIVE RESET: Clear all theme-related classes and storage
                localStorage.clear();
                localStorage.setItem('theme', 'light');

                // Remove all possible dark mode classes
                htmlElement.classList.remove('dark', 'dark-mode');
                htmlElement.classList.add('light', 'light-mode');
                htmlElement.setAttribute('data-theme', 'light');

                // Force light mode styles immediately
                htmlElement.style.colorScheme = 'light';

                // Debug: Log the current state
                console.log('Theme initialization:', {
                  classList: htmlElement.classList.toString(),
                  theme: localStorage.getItem('theme'),
                  colorScheme: htmlElement.style.colorScheme
                });
              })();
            `
          }}
        />
        {/* Additional SEO meta tags */}
        <meta name="geo.region" content="MA-09" />
        <meta name="geo.placename" content="Ourika Valley, Morocco" />
        <meta name="geo.position" content="31.2734;-7.8723" />
        <meta name="ICBM" content="31.2734, -7.8723" />
        
        {/* Structured data for local business */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "TravelAgency",
              "name": "Ourika Travels",
              "description": "Expert local guides for Ourika Valley tours and Atlas Mountains adventures in Morocco",
              "url": "https://ourikatravels.com",
              "logo": "https://ourikatravels.com/logo.png",
              "address": {
                "@type": "PostalAddress",
                "addressCountry": "MA",
                "addressRegion": "Marrakech-Safi",
                "addressLocality": "Ourika Valley"
              },
              "geo": {
                "@type": "GeoCoordinates",
                "latitude": "31.2734",
                "longitude": "-7.8723"
              },
              "areaServed": {
                "@type": "Place",
                "name": "Ourika Valley and Atlas Mountains, Morocco"
              },
              "serviceType": [
                "Guided Tours",
                "Hiking Expeditions",
                "Cultural Experiences",
                "Day Trips from Marrakech"
              ],
              "priceRange": "$$",
              "telephone": "+212-XXX-XXXXX", // Replace with actual phone
              "email": "<EMAIL>" // Replace with actual email
            })
          }}
        />
        
        {/* Preconnect to external domains for performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        
        {/* Favicon and app icons */}
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        
        {/* Theme color for mobile browsers */}
        <meta name="theme-color" content="#8B4513" />
        <meta name="msapplication-TileColor" content="#8B4513" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}