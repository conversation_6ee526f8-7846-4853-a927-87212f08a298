@import "tailwindcss";

:root {
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  margin: auto;
}

/* Light mode (default) */
:root,
html,
html.light,
html.light-mode,
html:not(.dark):not(.dark-mode) {
  --background: #ffffff;
  --foreground: #171717;
  --card: #ffffff;
  --card-foreground: #171717;
  --border: #e5e7eb;
  --input: #e5e7eb;
  --primary: #059669;
  --primary-foreground: #ffffff;
  --secondary: #f3f4f6;
  --secondary-foreground: #374151;
  --muted: #f9fafb;
  --muted-foreground: #6b7280;
  color-scheme: light;
}

/* Dark mode */
html.dark,
html.dark-mode {
  --background: #0a0a0a;
  --foreground: #ededed;
  --card: #1f2937;
  --card-foreground: #f9fafb;
  --border: #374151;
  --input: #374151;
  --primary: #10b981;
  --primary-foreground: #ffffff;
  --secondary: #374151;
  --secondary-foreground: #f9fafb;
  --muted: #111827;
  --muted-foreground: #9ca3af;
  color-scheme: dark;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Ensure theme switching works properly */
html {
  background: var(--background);
  color: var(--foreground);
}

/* Force light mode when no dark class */
html:not(.dark):not(.dark-mode) {
  background: #ffffff;
  color: #171717;
}

/* Force dark mode when dark class is present */
html.dark,
html.dark-mode {
  background: #0a0a0a;
  color: #ededed;
}

/* Body styles that respond to theme changes */
body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
  min-height: 100vh;
}

/* Smooth transitions for all elements */
* {
  transition-property: background-color, border-color, color, box-shadow;
  transition-duration: 0.3s;
  transition-timing-function: ease;
}

/* Custom utilities for modern effects */
@utility glassmorphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@utility glassmorphism-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-muted);
}

::-webkit-scrollbar-thumb {
  background: var(--color-primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary);
  opacity: 0.8;
}